<h1 align=center>Bookworm Light NextJs</h1> 
<p align=center>Bookworm Light is a minimal multi-author free nextjs blog template. This highly customizable theme is ideal for creating any type of blog website, including food, recipes, beauty, lifestyle, photography, travel, health, fitness, and more.</p>
<h2 align="center"> <a target="_blank" href="https://bookworm-light-nextjs.vercel.app/" rel="nofollow">👀Demo</a> | <a  target="_blank" href="https://pagespeed.web.dev/report?url=https%3A%2F%2Fbookworm-light-nextjs.vercel.app%2F&form_factor=desktop">Page Speed (100%)🚀</a>
</h2>



<p align=center>
  <a href="https://github.com/vercel/next.js/releases/tag/v13.0.6" alt="Contributors">
    <img src="https://img.shields.io/static/v1?label=NEXTJS&message=13.0&color=000&logo=nextjs" />
  </a>

  <a href="https://github.com/themefisher/bookworm-light-nextjs/blob/main/LICENSE">
    <img src="https://img.shields.io/github/license/themefisher/bookworm-light-nextjs" alt="license"></a>

  <img src="https://img.shields.io/github/languages/code-size/themefisher/bookworm-light-nextjs" alt="code size">

  <a href="https://github.com/themefisher/bookworm-light-nextjs/graphs/contributors">
    <img src="https://img.shields.io/github/contributors/themefisher/bigspring-light-nextjs" alt="contributors"></a>
</p>

![bookworm-light](https://demo.gethugothemes.com/thumbnails/bookworm-light.png)

## 🔑 Key Features

- 📄 13+ Pre-Designed Pages
- ✍️ Multi-Author support
- 🚀 Google Page Speed score 100! (Desktop)
- ✉️ Supports Contact Form
- 🔍 Search Functionality
- 🌐 Semantic HTML Document Structure
- 🖼️ Custom Logo Support
- 🎨 Includes All SCSS Files
- 🌅 Image Optimized With Next/image

## 📄 13+ Pre-Designed Pages

- 🏠 Home Page
-  ℹ️  About Page
- 📞 Contact Page
- 👤 Author Page
- 👤 Single Author Page
- 📚 Categories Page
- 📄 Categories Single Page
- 🔖 Tag Page
- 🔖 Single Tag pages
- 🎨 Elements Page
- 🔒 Privacy policy page
- 📚 Blog Page
- 📝 Blog Single Page



<!-- installation -->
## ⚙️ Installation

After downloading the template, you have some prerequisites to install. Then you can run it on your localhost. You can view the package.json file to see which scripts are included.

### 🔧 Install prerequisites (once for a machine)

* **Node Installation:** [Install node js](https://nodejs.org/en/download/) [Recommended LTS version]

### 🖥️ Local setup

After successfully installing those dependencies, open this template with any IDE [[VS Code](https://code.visualstudio.com/) recommended], and then open the internal terminal of IDM [vs code shortcut <code>ctrl/cmd+\`</code>]

* Install dependencies

```
npm install
```

* Run locally

```
npm run dev
```

After that, it will open up a preview of the template in your default browser, watch for changes to source files, and live-reload the browser when changes are saved.

## 🏗️ Production Build

After finishing all the customization, you can create a production build by running this command.

```
npm run build
```

<!-- reporting issue -->
## 🐞Reporting Issues

We use GitHub Issues as the official bug tracker for this Template. Please Search [existing issues](https://github.com/themefisher/bookworm-light-nextjs/issues). It’s possible someone has already reported the same problem.
If your problem or idea has not been addressed yet, feel free to [open a new issue](https://github.com/themefisher/bookworm-light-nextjs/issues).

<!-- support -->
## 💬 Technical Support or Customization (Paid)

If you need technical support or theme customization please [contact us](https://themefisher.com/contact) instead of opening an issue.

<!-- licence -->
## 📄 License

Copyright (c) 2016 - Present, Designed & Developed by [Themefisher](https://themefisher.com)

**Code License:** Released under the [MIT](https://github.com/themefisher/bookworm-light-nextjs/blob/main/LICENSE) license.

**Image license:** The images are only for demonstration purposes. They have their license, we don't have permission to share those images.

## 👨‍💻 Hire Us

Besides developing unique, blazing-fast Nextjs templates, we also provide customized services. We specialize in creating affordable, high-quality static websites based on Nextjs.

If you need to customize the theme or complete website development from scratch, you can **[hire us](https://themefisher.com/custom-development)**.

## 👉 More NextJs Templates By Us

| [![Spydea NextJs](https://demo.gethugothemes.com/thumbnails/spydea.png?)](https://themefisher.com/products/spydea-nextjs) | [![Logbook](https://demo.gethugothemes.com/thumbnails/andromeda.png)](https://themefisher.com/products/andromeda-nextjs) | [![Parsa](https://demo.gethugothemes.com/thumbnails/bigspring-light.png)](https://themefisher.com/products/bigspring-light-nextjs) |
|:---:|:---:|:---:|
| **Spydea NextJs** | **Andromeda NextJs** | **Bigspring Light NextJs** |

