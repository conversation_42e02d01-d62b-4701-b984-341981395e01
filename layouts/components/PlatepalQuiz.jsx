"use client";

import React, { useState } from "react";

const PlatepalQuiz = ({ questions }) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [redirecting, setRedirecting] = useState(false);

  const handleAnswer = () => {
    if (currentQuestionIndex + 1 < questions.length) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      setRedirecting(true);
      setTimeout(() => {
        window.location.href = "https://platepal.buildbyfeedback.com";
      }, 1500);
    }
  };

  if (!questions || questions.length === 0) {
    return null;
  }

  return (
    <div className="quiz-container max-w-xl mx-auto p-4 rounded shadow-md text-center text-black" style={{ backgroundColor: "#f2f2f2" }}>
      <h3 className="mb-4 text-lg font-semibold">{questions[currentQuestionIndex]}</h3>
      <div className="flex justify-center space-x-4">
        <button
          onClick={handleAnswer}
          className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
          disabled={redirecting}
        >
          Yes
        </button>
        <button
          onClick={handleAnswer}
          className="px-6 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition"
          disabled={redirecting}
        >
          No
        </button>
      </div>
      {redirecting && (
        <p className="mt-4 text-sm italic">Redirecting for direct access...</p>
      )}
    </div>
  );
};

export default PlatepalQuiz;
