import Pagination from "@components/Pagination";
import config from "@config/config.json";
import SeoMeta from "@layouts/partials/SeoMeta";
import { getSinglePage } from "@lib/contentParser";
import Posts from "@partials/Posts";
const { blog_folder } = config.settings;

// blog pagination
const BlogPagination = async ({ params }) => {
  console.log("===== BlogPagination START =====");
  console.log("Received params:", JSON.stringify(params, null, 2));

  const { slug } = params;
  const { pagination } = config.settings;
  
  console.log("Pagination setting:", pagination);
  console.log("Blog folder:", blog_folder);

  const posts = await getSinglePage(`content/${blog_folder}`);
  const authors = await getSinglePage("content/authors");

  console.log("Total posts count:", posts.length);
  console.log("Posts slugs:", posts.map(post => post.slug));

  let currentPosts;
  let totalPages = Math.ceil(posts.length / pagination);
  let currentPage = slug ? parseInt(slug) : 1;

  console.log("Current page:", currentPage);
  console.log("Total pages:", totalPages);

  // Pagination calculation logging
  const indexOfLastPost = currentPage * pagination;
  const indexOfFirstPost = indexOfLastPost - pagination;
  
  console.log("Index of first post:", indexOfFirstPost);
  console.log("Index of last post:", indexOfLastPost);

  currentPosts = posts.slice(indexOfFirstPost, indexOfLastPost);

  console.log("Current posts count:", currentPosts.length);
  console.log("Current posts slugs:", currentPosts.map(post => post.slug));

  if (currentPosts.length === 0) {
    console.error("No posts found for the current page");
    return <div>No posts found for this page</div>;
  }

  return (
    <>
      <SeoMeta title="Blog Pagination" />
      <section className="section">
        <div className="container">
          <Posts className="mb-16" posts={currentPosts} authors={authors} />
          <Pagination totalPages={totalPages} currentPage={currentPage} />
        </div>
      </section>
    </>
  );
};

export default BlogPagination;

// get blog pagination slug
export async function generateStaticParams() {
  console.log("===== generateStaticParams START =====");
  
  const getAllSlug = await getSinglePage(`content/${blog_folder}`);
  const allSlug = getAllSlug.map((item) => item.slug);
  
  const { pagination } = config.settings;
  const totalPages = Math.ceil(allSlug.length / pagination);
  
  console.log("Total posts:", allSlug.length);
  console.log("Pagination setting:", pagination);
  console.log("Total pages:", totalPages);

  let paths = [];

  for (let i = 1; i <= totalPages; i++) {
    paths.push({
      slug: i.toString(),
    });
  }

  // Add paths for individual posts
  allSlug.forEach((slug) => {
    paths.push({ slug });
  });

  console.log("Generated paths:", JSON.stringify(paths, null, 2));

  return paths;
}
