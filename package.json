{"name": "bookworm-light-nextjs", "author": "<PERSON><PERSON><PERSON>", "version": "2.0.0", "private": false, "license": "MIT", "scripts": {"dev": "node lib/jsonGenerator.js && next dev", "build": "node lib/jsonGenerator.js && next build", "export": "next build && next export", "lint": "next lint", "start": "next start"}, "dependencies": {"github-slugger": "^2.0.0", "gray-matter": "^4.0.3", "marked": "^14.1.0", "next": "^14.2.7", "next-mdx-remote": "^5.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-gtm-module": "^2.0.11", "react-icons": "^5.3.0", "rehype-highlight": "^7.0.0", "rehype-slug": "^6.0.0", "remark-gfm": "^4.0.0"}, "devDependencies": {"@tailwindcss/forms": "^0.5.8", "@tailwindcss/typography": "^0.5.15", "autoprefixer": "^10.4.20", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "eslint": "8.29.0", "eslint-config-next": "13.0.6", "jshint": "^2.13.6", "postcss": "^8.4.42", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.6", "sass": "^1.77.8", "sharp": "^0.33.5", "tailwind-bootstrap-grid": "^5.1.0", "tailwindcss": "^3.4.10"}}