import fs from "fs";
import matter from "gray-matter";
import path from "path";
import { sortByDate } from "./utils/sortFunctions";

// get list page data, ex: _index.md
export const getListPage = async (filePath) => {
  const pageData = fs.readFileSync(path.join(filePath), "utf-8");
  const pageDataParsed = matter(pageData);
  const notFoundPage = fs.readFileSync(path.join("content/404.md"), "utf-8");
  const notFoundDataParsed = matter(notFoundPage);
  let frontmatter, content;

  if (pageDataParsed) {
    content = pageDataParsed.content;
    frontmatter = pageDataParsed.data;
  } else {
    content = notFoundDataParsed.content;
    frontmatter = notFoundDataParsed.data;
  }

  return {
    frontmatter,
    content,
  };
};

// get all single pages, ex: blog/post.md
export const getSinglePage = (folder) => {
  const filesPath = fs.readdirSync(path.join(folder));
  const sanitizeFiles = filesPath.filter((file) => file.includes(".md"));
  const filterSingleFiles = sanitizeFiles.filter((file) =>
    file.match(/^(?!_)/)
  );
  const singlePages = filterSingleFiles.map((filename) => {
    const slug = filename.replace(".md", "");
    const pageData = fs.readFileSync(path.join(folder, filename), "utf-8");
    const pageDataParsed = matter(pageData);
    const frontmatterString = JSON.stringify(pageDataParsed.data);
    const frontmatter = JSON.parse(frontmatterString);
    const content = pageDataParsed.content;
    const url = frontmatter.url ? frontmatter.url.replace("/", "") : slug;
    return { frontmatter: frontmatter, slug: url, content: content };
  });

  const publishedPages = singlePages.filter(
    (page) =>
      !page.frontmatter.draft && page.frontmatter.layout !== "404" && page
  );
  const filterByDate = sortByDate(publishedPages);
  return filterByDate;
};

// get a regular page data from many pages, ex: about.md
export const getRegularPage = async (slug) => {
  let frontmatter, content;
  const publishedPages = getSinglePage("content/posts");
  const regularPage = getSinglePage("content");
  if (publishedPages.map((slug) => slug.slug).includes(slug)) {
    const pageData = publishedPages.filter((data) => data.slug === slug);
    content = pageData[0].content;
    frontmatter = pageData[0].frontmatter;
  } else if (regularPage.map((el) => el.slug).includes(slug)) {
    const regulerData = regularPage.filter((data) => data.slug === slug);
    content = regulerData[0].content;
    frontmatter = regulerData[0].frontmatter;
  } else {
    const notFoundPage = fs.readFileSync(path.join("content/404.md"), "utf-8");
    const notFoundDataParsed = matter(notFoundPage);
    content = notFoundDataParsed.content;
    frontmatter = notFoundDataParsed.data;
  }

  return {
    frontmatter,
    content,
  };
};
